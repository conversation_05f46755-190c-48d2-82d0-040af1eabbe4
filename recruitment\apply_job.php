<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

$job_id = $_GET['job_id'];
if(isset($_POST['apply'])){
    $cover = $_POST['cover_letter'];
    $conn->query("INSERT INTO job_applications (job_id,user_id,cover_letter) VALUES ($job_id,{$_SESSION['user_id']},'$cover')");
    $msg = "Application submitted!";
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Apply Job</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Apply for Job</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST">
    <textarea name="cover_letter" placeholder="Cover Letter" required></textarea>
    <button type="submit" name="apply">Submit Application</button>
</form>
<a href="job_list.php">Back</a>
</div>
</body>
</html>
