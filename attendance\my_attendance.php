<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

$records = $conn->query("SELECT * FROM attendance_records WHERE employee_id={$_SESSION['user_id']} ORDER BY date DESC");
?>
<!DOCTYPE html>
<html>
<head>
<title>My Attendance</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>My Attendance</header>
<div class="container">
<table>
<tr><th>Date</th><th>Status</th></tr>
<?php while($row=$records->fetch_assoc()){ ?>
<tr>
    <td><?= $row['date'] ?></td>
    <td><?= $row['status'] ?></td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/employee.php">Back</a>
</div>
</body>
</html>
