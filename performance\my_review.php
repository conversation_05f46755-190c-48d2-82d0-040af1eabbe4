<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

$reviews = $conn->query("SELECT * FROM performance_reviews WHERE employee_id={$_SESSION['user_id']}");
?>
<!DOCTYPE html>
<html>
<head>
<title>My Performance Review</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>My Performance Reviews</header>
<div class="container">
<table>
<tr><th>Score</th><th>Feedback</th></tr>
<?php while($row=$reviews->fetch_assoc()){ ?>
<tr>
    <td><?= $row['score'] ?></td>
    <td><?= $row['feedback'] ?></td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/employee.php">Back</a>
</div>
</body>
</html>
