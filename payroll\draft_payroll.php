<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Staff') { header("Location: ../auth/login.php"); exit; }

// Sample payroll: assume all employees have fixed salary 10000
if(isset($_POST['draft'])){
    $employee_id = $_POST['employee_id'];
    $amount = $_POST['amount'];
    $conn->query("INSERT INTO payroll (employee_id,amount,status) VALUES ($employee_id,$amount,'Draft')");
    $msg = "Payroll drafted!";
}

// Fetch all employees
$employees = $conn->query("SELECT * FROM users WHERE role='Employee'");
?>
<!DOCTYPE html>
<html>
<head>
<title>Draft Payroll</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Draft Payroll</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST">
    <select name="employee_id" required>
        <option value="">Select Employee</option>
        <?php while($row=$employees->fetch_assoc()){ ?>
        <option value="<?= $row['id'] ?>"><?= $row['username'] ?></option>
        <?php } ?>
    </select>
    <input type="number" name="amount" placeholder="Amount" required>
    <button type="submit" name="draft">Draft Payroll</button>
</form>
<a href="../dashboard/hr_staff.php">Back</a>
</div>
</body>
</html>
