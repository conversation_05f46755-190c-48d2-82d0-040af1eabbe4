<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

if(isset($_GET['approve'])){
    $conn->query("UPDATE job_applications SET status='Accepted' WHERE id=".$_GET['approve']);
} elseif(isset($_GET['reject'])){
    $conn->query("UPDATE job_applications SET status='Rejected' WHERE id=".$_GET['reject']);
}

$applications = $conn->query("SELECT ja.*, u.username, jp.title FROM job_applications ja JOIN users u ON ja.user_id=u.id JOIN job_posts jp ON ja.job_id=jp.id ORDER BY applied_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
<title>Job Applications</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Job Applications</header>
<div class="container">
<table>
<tr><th>Applicant</th><th>Job</th><th>Status</th><th>Action</th></tr>
<?php while($row=$applications->fetch_assoc()){ ?>
<tr>
    <td><?= $row['username'] ?></td>
    <td><?= $row['title'] ?></td>
    <td><?= $row['status'] ?></td>
    <td>
        <?php if($row['status']=='Pending'){ ?>
        <a href="?approve=<?= $row['id'] ?>">Approve</a> | 
        <a href="?reject=<?= $row['id'] ?>">Reject</a>
        <?php } ?>
    </td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/hr_head.php">Back</a>
</div>
</body>
</html>
