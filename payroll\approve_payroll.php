<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

if(isset($_GET['approve'])){
    $conn->query("UPDATE payroll SET status='Approved' WHERE id=".$_GET['approve']);
}

$payrolls = $conn->query("SELECT p.*, u.username FROM payroll p JOIN users u ON p.employee_id=u.id ORDER BY id DESC");
?>
<!DOCTYPE html>
<html>
<head>
<title>Approve Payroll</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Approve Payroll</header>
<div class="container">
<table>
<tr><th>Employee</th><th>Amount</th><th>Status</th><th>Action</th></tr>
<?php while($row=$payrolls->fetch_assoc()){ ?>
<tr>
    <td><?= $row['username'] ?></td>
    <td><?= $row['amount'] ?></td>
    <td><?= $row['status'] ?></td>
    <td><?php if($row['status']=='Draft'){ ?><a href="?approve=<?= $row['id'] ?>">Approve</a><?php } ?></td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/hr_head.php">Back</a>
</div>
</body>
</html>
