<?php
session_start();
include "../config/db.php";

// Only Admin can register
if($_SESSION['role'] !== 'Admin') { header("Location: ../auth/login.php"); exit; }

if(isset($_POST['register'])){
    $username = $_POST['username'];
    $password = md5($_POST['password']);
    $role = $_POST['role'];

    $conn->query("INSERT INTO users (username,password,role) VALUES ('$username','$password','$role')");
    $msg = "User created successfully!";
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Register User</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Register New User</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST">
    <input type="text" name="username" placeholder="Username" required>
    <input type="password" name="password" placeholder="Password" required>
    <select name="role" required>
        <option value="">Select Role</option>
        <option value="Admin">Admin</option>
        <option value="HR Head">HR Head</option>
        <option value="HR Staff">HR Staff</option>
        <option value="Employee">Employee</option>
    </select>
    <button type="submit" name="register">Register</button>
</form>
<a href="../dashboard/admin.php">Back to Dashboard</a>
</div>
</body>
</html>
