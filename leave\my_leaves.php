<?php
session_start();
include "../config/db.php";

$user_id = $_SESSION['user_id'];
$role = $_SESSION['role'];

if(!in_array($role,['Employee','HR Staff'])) { header("Location: ../auth/login.php"); exit; }

if($role === 'Employee'){
    $leaves = $conn->query("SELECT * FROM leave_requests WHERE user_id=$user_id ORDER BY requested_at DESC");
} else {
    $leaves = $conn->query("SELECT lr.*, u.username FROM leave_requests lr JOIN users u ON lr.user_id=u.id ORDER BY requested_at DESC");
}
?>
<!DOCTYPE html>
<html>
<head>
<title>My Leaves</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Leave Requests</header>
<div class="container">
<table>
<tr><th>User</th><th>Type</th><th>Start</th><th>End</th><th>Status</th></tr>
<?php while($row = $leaves->fetch_assoc()){ ?>
<tr>
    <td><?= $role==='Employee' ? $_SESSION['username'] : $row['username'] ?></td>
    <td><?= $row['type'] ?></td>
    <td><?= $row['start_date'] ?></td>
    <td><?= $row['end_date'] ?></td>
    <td><?= $row['status'] ?></td>
</tr>
<?php } ?>
</table>
<a href="<?= $role==='Employee' ? '../dashboard/employee.php' : '../dashboard/hr_staff.php' ?>">Back</a>
</div>
</body>
</html>
