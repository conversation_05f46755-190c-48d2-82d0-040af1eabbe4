/* ==========================
   COLORS & ROOT VARIABLES
========================== */
:root {
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --background: #f0f5fb;
    --text-color: #1a1a1a;
    --card-bg: #ffffff;
    --card-shadow: rgba(0,0,0,0.1);
}

/* ==========================
   GLOBAL STYLES
========================== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    background-color: var(--background);
    color: var(--text-color);
}

a {
    text-decoration: none;
    color: inherit;
}

h3 {
    margin: 0;
}

/* ==========================
   SIDEBAR
========================== */
.sidebar {
    height: 100vh;
    width: 220px;
    background-color: var(--primary-blue);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    overflow-x: hidden;
    padding-top: 20px;
    transition: 0.3s;
}

.sidebar.collapsed {
    width: 60px;
}

.sidebar a {
    display: block;
    padding: 12px 20px;
    color: white;
    margin: 5px 0;
    border-radius: 5px;
    transition: 0.3s;
}

.sidebar a:hover {
    background-color: var(--secondary-blue);
}

.closebtn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    width: 100%;
    text-align: left;
    margin-bottom: 20px;
}

/* ==========================
   MAIN CONTENT
========================== */
.main-content {
    margin-left: 220px;
    padding: 20px;
    transition: 0.3s;
}

.main-content.expanded {
    margin-left: 60px;
}

/* ==========================
   HEADER
========================== */
header {
    background-color: var(--primary-blue);
    color: white;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-size: 20px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* ==========================
   CARDS
========================== */
.card {
    background: var(--card-bg);
    padding: 20px;
    margin: 15px;
    border-radius: 10px;
    display: inline-block;
    width: 200px;
    box-shadow: 0 4px 6px var(--card-shadow);
    transition: 0.3s;
    vertical-align: top;
}

.card:hover {
    box-shadow: 0 8px 12px var(--card-shadow);
}

.card h3 {
    color: var(--primary-blue);
}

/* ==========================
   TABLES
========================== */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    box-shadow: 0 0 10px var(--card-shadow);
    border-radius: 5px;
    overflow: hidden;
}

table th, table td {
    padding: 12px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

table th {
    background-color: var(--primary-blue);
    color: white;
}

table tr:nth-child(even) {
    background-color: #f9f9f9;
}

table tr:hover {
    background-color: #dbeafe;
}

/* ==========================
   BUTTONS
========================== */
button {
    background-color: var(--secondary-blue);
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.3s;
}

button:hover {
    background-color: var(--primary-blue);
}

/* ==========================
   FORMS
========================== */
input, select, textarea {
    width: 100%;
    padding: 10px;
    margin: 8px 0 15px 0;
    border-radius: 5px;
    border: 1px solid #ccc;
}

textarea {
    resize: vertical;
}

/* ==========================
   RESPONSIVE
========================== */
@media screen and (max-width: 768px) {
    .sidebar {
        width: 60px;
    }
    .main-content {
        margin-left: 60px;
    }
    .card {
        width: 100%;
    }
}

