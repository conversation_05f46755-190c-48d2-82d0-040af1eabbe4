<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

$payrolls = $conn->query("SELECT * FROM payroll WHERE employee_id={$_SESSION['user_id']} AND status='Approved'");
?>
<!DOCTYPE html>
<html>
<head>
<title>My Payslip</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>My Payslip</header>
<div class="container">
<table>
<tr><th>Amount</th><th>Status</th></tr>
<?php while($row=$payrolls->fetch_assoc()){ ?>
<tr>
    <td><?= $row['amount'] ?></td>
    <td><?= $row['status'] ?></td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/employee.php">Back</a>
</div>
</body>
</html>
