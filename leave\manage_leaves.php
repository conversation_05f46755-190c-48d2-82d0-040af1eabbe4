<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

if(isset($_GET['approve'])){
    $id = $_GET['approve'];
    $conn->query("UPDATE leave_requests SET status='Approved' WHERE id=$id");
} elseif(isset($_GET['reject'])){
    $id = $_GET['reject'];
    $conn->query("UPDATE leave_requests SET status='Rejected' WHERE id=$id");
}

$leaves = $conn->query("SELECT lr.*, u.username FROM leave_requests lr JOIN users u ON lr.user_id=u.id ORDER BY requested_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
<title>Manage Leaves</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Manage Leave Requests</header>
<div class="container">
<table>
<tr><th>User</th><th>Type</th><th>Start</th><th>End</th><th>Status</th><th>Action</th></tr>
<?php while($row=$leaves->fetch_assoc()){ ?>
<tr>
    <td><?= $row['username'] ?></td>
    <td><?= $row['type'] ?></td>
    <td><?= $row['start_date'] ?></td>
    <td><?= $row['end_date'] ?></td>
    <td><?= $row['status'] ?></td>
    <td>
        <?php if($row['status']=='Pending'){ ?>
        <a href="?approve=<?= $row['id'] ?>">Approve</a> |
        <a href="?reject=<?= $row['id'] ?>">Reject</a>
        <?php } ?>
    </td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/hr_head.php">Back</a>
</div>
</body>
</html>
