<?php
session_start();
include "../config/db.php";

if(isset($_POST['login'])){
    $username = $_POST['username'];
    $password = md5($_POST['password']);
    
    $sql = "SELECT * FROM users WHERE username='$username' AND password='$password'";
    $result = $conn->query($sql);
    
    if($result->num_rows > 0){
        $user = $result->fetch_assoc();
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        
        switch($user['role']){
            case 'Admin': header("Location: ../dashboard/admin.php"); break;
            case 'HR Head': header("Location: ../dashboard/hr_head.php"); break;
            case 'HR Staff': header("Location: ../dashboard/hr_staff.php"); break;
            case 'Employee': header("Location: ../dashboard/employee.php"); break;
        }
    } else {
        $error = "Invalid username or password";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Login</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Login</header>
<div class="container">
<?php if(isset($error)) echo "<p>$error</p>"; ?>
<form method="POST">
    <input type="text" name="username" placeholder="Username" required>
    <input type="password" name="password" placeholder="Password" required>
    <button type="submit" name="login">Login</button>
</form>
</div>
</body>
</html>
