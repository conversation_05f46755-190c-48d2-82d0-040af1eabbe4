<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Head') { header("Location: ../auth/login.php"); exit; }

if(isset($_POST['review'])){
    $employee_id = $_POST['employee_id'];
    $score = $_POST['score'];
    $feedback = $_POST['feedback'];
    $conn->query("INSERT INTO performance_reviews (employee_id,score,feedback) VALUES ($employee_id,$score,'$feedback')");
    $msg = "Performance review added!";
}

$employees = $conn->query("SELECT * FROM users WHERE role='Employee'");
?>
<!DOCTYPE html>
<html>
<head>
<title>Add Performance Review</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Add Performance Review</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST">
    <select name="employee_id" required>
        <option value="">Select Employee</option>
        <?php while($row=$employees->fetch_assoc()){ ?>
        <option value="<?= $row['id'] ?>"><?= $row['username'] ?></option>
        <?php } ?>
    </select>
    <input type="number" name="score" placeholder="Score (0-100)" required>
    <textarea name="feedback" placeholder="Feedback"></textarea>
    <button type="submit" name="review">Add Review</button>
</form>
<a href="../dashboard/hr_head.php">Back</a>
</div>
</body>
</html>
