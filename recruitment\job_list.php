<?php
session_start();
include "../config/db.php";
$jobs = $conn->query("SELECT jp.*, u.username AS posted_by FROM job_posts jp JOIN users u ON jp.posted_by=u.id ORDER BY posted_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
<title>Job List</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Available Jobs</header>
<div class="container">
<table>
<tr><th>Title</th><th>Description</th><th>Posted By</th><th>Action</th></tr>
<?php while($row=$jobs->fetch_assoc()){ ?>
<tr>
    <td><?= $row['title'] ?></td>
    <td><?= $row['description'] ?></td>
    <td><?= $row['posted_by'] ?></td>
    <td><a href="apply_job.php?job_id=<?= $row['id'] ?>">Apply</a></td>
</tr>
<?php } ?>
</table>
<a href="../dashboard/employee.php">Back</a>
</div>
</body>
</html>
