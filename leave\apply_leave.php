<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'Employee') { header("Location: ../auth/login.php"); exit; }

if(isset($_POST['apply'])){
    $type = $_POST['type'];
    $start = $_POST['start_date'];
    $end = $_POST['end_date'];
    $reason = $_POST['reason'];

    $conn->query("INSERT INTO leave_requests (user_id,type,start_date,end_date,reason) VALUES ({$_SESSION['user_id']},'$type','$start','$end','$reason')");
    $msg = "Leave request submitted!";
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Apply Leave</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Apply Leave</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST">
    <select name="type" required>
        <option value="">Select Leave Type</option>
        <option value="Vacation">Vacation</option>
        <option value="Sick">Sick</option>
        <option value="Emergency">Emergency</option>
        <option value="Other">Other</option>
    </select>
    <input type="date" name="start_date" required>
    <input type="date" name="end_date" required>
    <textarea name="reason" placeholder="Reason"></textarea>
    <button type="submit" name="apply">Submit</button>
</form>
<a href="../dashboard/employee.php">Back</a>
</div>
</body>
</html>
