<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Staff') { header("Location: ../auth/login.php"); exit; }

if(isset($_POST['post'])){
    $title = $_POST['title'];
    $desc = $_POST['description'];
    $conn->query("INSERT INTO job_posts (title,description,posted_by) VALUES ('$title','$desc',{$_SESSION['user_id']})");
    $msg = "Job posted successfully!";
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Post Job</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Post New Job</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST">
    <input type="text" name="title" placeholder="Job Title" required>
    <textarea name="description" placeholder="Job Description" required></textarea>
    <button type="submit" name="post">Post Job</button>
</form>
<a href="../dashboard/hr_staff.php">Back</a>
</div>
</body>
</html>
