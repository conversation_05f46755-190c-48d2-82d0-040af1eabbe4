<?php
session_start();
include "../config/db.php";
if($_SESSION['role'] !== 'HR Staff') { header("Location: ../auth/login.php"); exit; }

if(isset($_POST['upload'])){
    $file = $_FILES['attendance']['name'];
    $tmp = $_FILES['attendance']['tmp_name'];
    move_uploaded_file($tmp, "../attendance_uploads/$file");
    $conn->query("INSERT INTO attendance_uploads (file_name,uploaded_by) VALUES ('$file',{$_SESSION['user_id']})");
    $upload_id = $conn->insert_id;

    if (($handle = fopen("../attendance_uploads/$file", "r")) !== FALSE) {
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $emp_id = $data[0];
            $date = $data[1];
            $status = $data[2];
            $conn->query("INSERT INTO attendance_records (upload_id,employee_id,date,status) VALUES ($upload_id,$emp_id,'$date','$status')");
        }
        fclose($handle);
    }
    $msg = "Attendance uploaded successfully!";
}
?>
<!DOCTYPE html>
<html>
<head>
<title>Upload Attendance</title>
<link rel="stylesheet" href="../assets/style.css">
</head>
<body>
<header>Upload Attendance CSV</header>
<div class="container">
<?php if(isset($msg)) echo "<p>$msg</p>"; ?>
<form method="POST" enctype="multipart/form-data">
    <input type="file" name="attendance" required>
    <button type="submit" name="upload">Upload</button>
</form>
<a href="../dashboard/hr_staff.php">Back</a>
</div>
</body>
</html>
